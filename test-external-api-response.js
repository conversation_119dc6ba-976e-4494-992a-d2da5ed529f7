// Test script to check what the external API actually returns
const API_BASE_URL = 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

async function testExternalAPI() {
  console.log('🔍 Testing External API Response Format\n');

  // Test with a fake token to see what happens when no apps are found
  try {
    console.log('1. Testing GET /apps with fake token...');
    const response = await fetch(`${API_BASE_URL}/apps`, {
      headers: {
        'Authorization': 'Bearer fake-token-for-testing',
        'ngrok-skip-browser-warning': 'true'
      }
    });

    const data = await response.text();
    console.log('✅ Response Status:', response.status);
    console.log('✅ Response OK:', response.ok);
    console.log('✅ Response Data (raw):', data);

    try {
      const jsonData = JSON.parse(data);
      console.log('✅ Response Data (JSON):', JSON.stringify(jsonData, null, 2));
    } catch (e) {
      console.log('❌ Response is not valid JSON');
    }

  } catch (error) {
    console.log('❌ Network Error:', error.message);
  }

  // Test login to see response format
  try {
    console.log('\n2. Testing POST /developers/login with real credentials...');
    const response = await fetch(`${API_BASE_URL}/developers/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '123456'
      })
    });

    const data = await response.text();
    console.log('✅ Login Response Status:', response.status);
    console.log('✅ Login Response OK:', response.ok);
    console.log('✅ Login Response Data (raw):', data);

    let token = null;
    try {
      const jsonData = JSON.parse(data);
      console.log('✅ Login Response Data (JSON):', JSON.stringify(jsonData, null, 2));

      // Extract token if login was successful
      if (jsonData.token) {
        token = jsonData.token;
        console.log('🔑 Token extracted for further testing');
      }
    } catch (e) {
      console.log('❌ Login Response is not valid JSON');
    }

    // If we got a token, test the apps endpoint
    if (token) {
      try {
        console.log('\n3. Testing GET /apps with real token...');
        const appsResponse = await fetch(`${API_BASE_URL}/apps`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': 'true'
          }
        });

        const appsData = await appsResponse.text();
        console.log('✅ Apps Response Status:', appsResponse.status);
        console.log('✅ Apps Response OK:', appsResponse.ok);
        console.log('✅ Apps Response Data (raw):', appsData);

        try {
          const appsJsonData = JSON.parse(appsData);
          console.log('✅ Apps Response Data (JSON):', JSON.stringify(appsJsonData, null, 2));

          // Test what our API service would return
          console.log('\n🔧 Testing our API service logic:');
          if (appsJsonData && typeof appsJsonData === 'object' && 'success' in appsJsonData) {
            if (appsJsonData.success === false) {
              console.log('❌ Our API would return: { success: false, error: "' + (appsJsonData.message || appsJsonData.error) + '" }');
            } else {
              console.log('✅ Our API would return: { success: true, data: [...] }');
              console.log('   Data field contains:', Array.isArray(appsJsonData.data) ? `${appsJsonData.data.length} apps` : typeof appsJsonData.data);
            }
          }
        } catch (e) {
          console.log('❌ Apps Response is not valid JSON');
        }

      } catch (error) {
        console.log('❌ Apps Network Error:', error.message);
      }
    }

  } catch (error) {
    console.log('❌ Login Network Error:', error.message);
  }

  // Test registering a new developer to see what an empty apps response looks like
  try {
    console.log('\n4. Testing developer registration to get empty apps response...');
    const testEmail = `test${Date.now()}@example.com`;

    const registerResponse = await fetch(`${API_BASE_URL}/developers/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      body: JSON.stringify({
        name: 'Test User',
        email: testEmail,
        password: '123456'
      })
    });

    const registerData = await registerResponse.text();
    console.log('✅ Register Response Status:', registerResponse.status);
    console.log('✅ Register Response Data:', registerData);

    // Note: This will likely require OTP verification, so we can't complete the full flow
    // But we can see the registration response format

  } catch (error) {
    console.log('❌ Registration test error:', error.message);
  }
}

testExternalAPI().catch(console.error);
