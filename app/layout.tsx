import { Theme<PERSON>rovider } from "@/components/shared/theme-toggle"
import { AuthProvider } from "@/lib/auth-context"
import { Toaster } from "sonner"
import { RainbowKitProviders } from '@/app/providers/rainbowkit-provider'
import '@/lib/polyfills'
import "./globals.css"
import { Inter, Space_Grotesk, Orbitron, DM_Sans } from 'next/font/google'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const spaceGrotesk = Space_Grotesk({
  subsets: ['latin'],
  variable: '--font-space-grotesk',
  display: 'swap',
})

const orbitron = Orbitron({
  subsets: ['latin'],
  variable: '--font-orbitron',
  display: 'swap',
})

const dmSans = DM_Sans({
  subsets: ['latin'],
  variable: '--font-dm-sans',
  display: 'swap',
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className={`${inter.variable} ${spaceGrotesk.variable} ${orbitron.variable} ${dmSans.variable}`}>
      <body className="min-h-screen bg-background font-body antialiased">
        {/* Full page fixed background image */}
        <div
          className="absolute inset-0 -z-10 bg-cover bg-fixed bg-center"
          style={{ backgroundImage: "url('https://images.unsplash.com/photo-1518655048521-f130df041f66?q=80&w=2070&auto=format&fit=crop')" }} // Example futuristic/tech image
        />
        <RainbowKitProviders>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark" // Forcing dark theme as per design aesthetic
            enableSystem={false} // System theme preference disabled to enforce dark theme
            disableTransitionOnChange
          >
            <AuthProvider>
              {children}
              <Toaster position="top-right" />
            </AuthProvider>
          </ThemeProvider>
        </RainbowKitProviders>
      </body>
    </html>
  )
}