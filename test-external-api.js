// Test script for External Crefy Connect API
// Run with: node test-external-api.js

const axios = require('axios');

const API_BASE_URL = 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

async function testExternalAPI() {
  console.log('🚀 Testing External Crefy Connect API\n');

  // Test 1: Register a developer
  console.log('1. Testing developer registration...');
  try {
    const registerResponse = await axios.post(`${API_BASE_URL}/developers/register`, {
      name: 'Test Developer',
      email: '<EMAIL>',
      password: 'testpassword123'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    console.log('✅ Register Response Status:', registerResponse.status);
    console.log('✅ Register Response Data:', JSON.stringify(registerResponse.data, null, 2));
  } catch (error) {
    console.log('❌ Register Error Status:', error.response?.status);
    console.log('❌ Register Error Data:', JSON.stringify(error.response?.data, null, 2));
  }

  // Test 2: Login with developer credentials
  console.log('\n2. Testing developer login...');
  try {
    const loginResponse = await axios.post(`${API_BASE_URL}/developers/login`, {
      email: '<EMAIL>',
      password: 'testpassword123'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      }
    });
    
    console.log('✅ Login Response Status:', loginResponse.status);
    console.log('✅ Login Response Data:', JSON.stringify(loginResponse.data, null, 2));
    
    const token = loginResponse.data.token;
    
    if (token) {
      // Test 3: Get developer profile
      console.log('\n3. Testing get developer profile...');
      try {
        const profileResponse = await axios.get(`${API_BASE_URL}/developers/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': 'true'
          }
        });
        
        console.log('✅ Profile Response Status:', profileResponse.status);
        console.log('✅ Profile Response Data:', JSON.stringify(profileResponse.data, null, 2));
      } catch (error) {
        console.log('❌ Profile Error Status:', error.response?.status);
        console.log('❌ Profile Error Data:', JSON.stringify(error.response?.data, null, 2));
      }

      // Test 4: Get applications
      console.log('\n4. Testing get applications...');
      try {
        const appsResponse = await axios.get(`${API_BASE_URL}/apps`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': 'true'
          }
        });
        
        console.log('✅ Apps Response Status:', appsResponse.status);
        console.log('✅ Apps Response Data:', JSON.stringify(appsResponse.data, null, 2));
      } catch (error) {
        console.log('❌ Apps Error Status:', error.response?.status);
        console.log('❌ Apps Error Data:', JSON.stringify(error.response?.data, null, 2));
      }

      // Test 5: Create an application
      console.log('\n5. Testing create application...');
      try {
        const createAppResponse = await axios.post(`${API_BASE_URL}/apps`, {
          name: 'Test Application',
          description: 'A test application',
          redirectUrls: ['https://example.com/callback'],
          iconUrl: 'https://example.com/icon.png'
        }, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'ngrok-skip-browser-warning': 'true'
          }
        });
        
        console.log('✅ Create App Response Status:', createAppResponse.status);
        console.log('✅ Create App Response Data:', JSON.stringify(createAppResponse.data, null, 2));
      } catch (error) {
        console.log('❌ Create App Error Status:', error.response?.status);
        console.log('❌ Create App Error Data:', JSON.stringify(error.response?.data, null, 2));
      }
    }
    
  } catch (error) {
    console.log('❌ Login Error Status:', error.response?.status);
    console.log('❌ Login Error Data:', JSON.stringify(error.response?.data, null, 2));
  }
}

// Run the test
testExternalAPI().catch(console.error);
