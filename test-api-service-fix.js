// Test the fixed API service
import { apiService } from './lib/api.js';

async function testApiServiceFix() {
  console.log('🧪 Testing Fixed API Service\n');

  try {
    // Test login
    console.log('1. Testing login...');
    const loginResult = await apiService.login({
      email: '<EMAIL>',
      password: '123456'
    });
    
    console.log('Login result:', JSON.stringify(loginResult, null, 2));
    
    if (loginResult.success && loginResult.data?.token) {
      const token = loginResult.data.token;
      console.log('✅ Login successful, got token');
      
      // Test getting applications
      console.log('\n2. Testing getApplications...');
      const appsResult = await apiService.getApplications(token);
      console.log('Apps result:', JSON.stringify(appsResult, null, 2));
      
      if (appsResult.success) {
        console.log('✅ getApplications returned success:', appsResult.success);
        console.log('✅ Data type:', typeof appsResult.data);
        console.log('✅ Data is array:', Array.isArray(appsResult.data));
        if (Array.isArray(appsResult.data)) {
          console.log('✅ Number of apps:', appsResult.data.length);
        }
      } else {
        console.log('❌ getApplications failed:', appsResult.error);
      }
      
    } else {
      console.log('❌ Login failed:', loginResult.error);
    }
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testApiServiceFix();
