module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './lib/**/*.{js,ts,jsx,tsx,mdx}',
    './hooks/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class', // The new design seems to imply a dark theme primarily, ensure this is handled.
  theme: {
    container: {
      center: true,
      padding: '1.5rem', // Default padding for containers
    },
    extend: {
      colors: {
        accent: { // Updated accent with lavender
          DEFAULT: '#C7B1E6', // Lavender
          dark: '#9D81D1',
        },
        deep: { // Deep purple
          DEFAULT: '#4B0082', // Indigo/Deep Purple
          dark: '#35005A',
        },
        'brand-purple-deep': '#4B0082', // Deep Purple for gradient from
        'brand-purple-light': '#B497D6', // Lavender for gradient to
        'brand-purple-medium': '#6A0DAD', // Medium purple for accent elements
        'brand-indigo': '#4B0082', // Indigo
        'brand-lavender': '#C7B1E6', // Lavender
        'neon-accent': '#A06FE9', // Changed to a purple accent
        'soft-gray': '#B8B8B8', // Slightly lighter for better contrast on white
        dark: { // Existing dark theme colors
          bg: '#FFFFFF', // Changed to white as base
          card: '#FCFCFC',
          text: '#333333',
        },
      },
      fontFamily: {
        // Default body font using CSS variables
        sans: ['var(--font-inter)', 'var(--font-dm-sans)', 'sans-serif'],
        // For headings
        heading: ['var(--font-space-grotesk)', 'var(--font-orbitron)', 'sans-serif'],
        // Existing mono
        mono: ['"Space Mono"', 'monospace'],
        // Explicitly define body font variable for clarity in RootLayout if needed
        body: ['var(--font-inter)', 'var(--font-dm-sans)', 'sans-serif'],
        // Individual font variables for specific use
        inter: ['var(--font-inter)', 'sans-serif'],
        'space-grotesk': ['var(--font-space-grotesk)', 'sans-serif'],
        orbitron: ['var(--font-orbitron)', 'sans-serif'],
        'dm-sans': ['var(--font-dm-sans)', 'sans-serif'],
      },
      borderRadius: {
        '2xl': '1rem', // Existing
        'full': '9999px', // For rounded-full elements like the navbar
      },
      backgroundImage: {
        // Example for direct use in Tailwind classes if needed, though inline style is also fine
        'hero-pattern': "url('/your-image.jpg')",
      },
      boxShadow: {
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)', // Example, can be customized
        'soft': '0 10px 25px -5px rgba(255, 255, 255, 0.05), 0 8px 10px -6px rgba(255, 255, 255, 0.05)', // Soft shadow for transparent elements
      },
      animation: {
        'fade-up': 'fadeInUp 0.5s ease-out',
        'slide-in': 'slideIn 0.3s ease-out',
        // Add other animations as needed
      },
      // Keyframes might be needed for more complex animations
    },
  },
  plugins: [
    // require('@tailwindcss/typography'), // If rich text styling is needed
  ],
}

