// Test external API authentication
// This will help us register and test the external API

const API_BASE_URL = 'https://api.crefy-connect-v2.crefy.xyz/api/v1';

async function testExternalAuth() {
  console.log('🚀 Testing External Crefy Connect API Authentication\n');
  console.log('API Base URL:', API_BASE_URL);

  // Test 1: Try to register a developer
  console.log('\n1. Testing developer registration...');
  
  const testEmail = '<EMAIL>';
  const testPassword = 'testpassword123';
  const testName = 'Test Developer';

  try {
    const response = await fetch(`${API_BASE_URL}/developers/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      body: JSON.stringify({
        name: testName,
        email: testEmail,
        password: testPassword
      })
    });

    const data = await response.json();
    console.log('Register Response Status:', response.status);
    console.log('Register Response:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('✅ Registration successful!');
      
      // If registration requires OTP verification, we'll need to handle that
      if (data.message && data.message.includes('OTP')) {
        console.log('📧 OTP verification required. Check your email or use a test OTP.');
        
        // Test with a common test OTP (this might not work, but worth trying)
        console.log('\n2. Testing OTP verification with test code...');
        try {
          const otpResponse = await fetch(`${API_BASE_URL}/developers/verify-otp`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'ngrok-skip-browser-warning': 'true'
            },
            body: JSON.stringify({
              email: testEmail,
              otp: '123456' // Common test OTP
            })
          });

          const otpData = await otpResponse.json();
          console.log('OTP Response Status:', otpResponse.status);
          console.log('OTP Response:', JSON.stringify(otpData, null, 2));
        } catch (error) {
          console.log('❌ OTP verification failed:', error.message);
        }
      }
    } else {
      console.log('❌ Registration failed');
    }

  } catch (error) {
    console.log('❌ Registration error:', error.message);
  }

  // Test 2: Try to login
  console.log('\n3. Testing developer login...');
  try {
    const loginResponse = await fetch(`${API_BASE_URL}/developers/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'ngrok-skip-browser-warning': 'true'
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login Response Status:', loginResponse.status);
    console.log('Login Response:', JSON.stringify(loginData, null, 2));

    if (loginResponse.ok && loginData.token) {
      console.log('✅ Login successful!');
      const token = loginData.token;

      // Test 3: Try to get profile
      console.log('\n4. Testing get profile...');
      try {
        const profileResponse = await fetch(`${API_BASE_URL}/developers/profile`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': 'true'
          }
        });

        const profileData = await profileResponse.json();
        console.log('Profile Response Status:', profileResponse.status);
        console.log('Profile Response:', JSON.stringify(profileData, null, 2));

        if (profileResponse.ok) {
          console.log('✅ Profile fetch successful!');
        }
      } catch (error) {
        console.log('❌ Profile fetch error:', error.message);
      }

      // Test 4: Try to get applications
      console.log('\n5. Testing get applications...');
      try {
        const appsResponse = await fetch(`${API_BASE_URL}/apps`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'ngrok-skip-browser-warning': 'true'
          }
        });

        const appsData = await appsResponse.json();
        console.log('Apps Response Status:', appsResponse.status);
        console.log('Apps Response:', JSON.stringify(appsData, null, 2));

        if (appsResponse.ok) {
          console.log('✅ Applications fetch successful!');
        }
      } catch (error) {
        console.log('❌ Applications fetch error:', error.message);
      }

    } else {
      console.log('❌ Login failed');
    }

  } catch (error) {
    console.log('❌ Login error:', error.message);
  }

  console.log('\n🎉 External API testing completed!');
  console.log('\nNext steps:');
  console.log('1. If registration worked, use the same credentials in the app');
  console.log('2. If OTP verification is required, check the email or contact API admin');
  console.log('3. Make sure to use the token from the external API login');
}

// Run the test
testExternalAuth().catch(console.error);
